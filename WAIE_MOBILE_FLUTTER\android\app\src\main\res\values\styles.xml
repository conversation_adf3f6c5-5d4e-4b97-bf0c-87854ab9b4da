<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!-- Theme applied to the Android Window while the process is starting when the OS's Dark Mode setting is off -->
    <style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <!-- Remove window background to disable splash screen -->
        <item name="android:windowBackground">?android:colorBackground</item>
    </style>

    <!-- Main App Theme -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Remove splash screen background -->
        <item name="android:windowBackground">?android:colorBackground</item>
    </style>

    <!-- Normal Theme (used once Flutter loads) -->
    <style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style>

</resources>
