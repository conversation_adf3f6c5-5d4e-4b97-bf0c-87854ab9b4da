//package com.waie.waie_app;
package com.waie.aldrees.mobile;

import static com.waie.aldrees.mobile.interface_classes.MadaApiService.MADA_MID;
import static com.waie.aldrees.mobile.interface_classes.MadaApiService.MADA_URL;

import android.provider.Settings;
import android.widget.Toast;

import androidx.annotation.NonNull;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import com.mastercard.gateway.android.sdk.GatewayMap;
import com.waie.aldrees.mobile.interface_classes.CreateSessionCallback;
import com.waie.aldrees.mobile.interface_classes.Gateway;
import com.waie.aldrees.mobile.interface_classes.MadaApiService;

import java.util.Map;
import java.util.UUID;

import io.flutter.Log;
import io.flutter.embedding.android.FlutterFragmentActivity;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugin.common.MethodChannel;

public class MainActivity extends FlutterFragmentActivity {
    private final String channelName = "flutter.native.helper";
    private static String orderId = "";
    static MethodChannel channel;

    @Override
    public void configureFlutterEngine(@NonNull FlutterEngine flutterEngine) {
        super.configureFlutterEngine(flutterEngine);

        new MethodChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), channelName).setMethodCallHandler(
                (call, result) -> {
                    if ("getAndroidId".equals(call.method)) {
                        getAndroidId(result);
                    } else if ("MADAPayment".equals(call.method)) {
                        Map<String, String> args = (Map<String, String>) call.arguments;
                        String totalAmount = args.get("totalPurchased");
                        String SessionID = args.get("sessionID");
                        String orderID = args.get("orderID");
                        String serviceType = args.get("serviceType");
                        String orderType = args.get("orderType");
                        String custid = args.get("custid");
                        String qty = args.get("qty");

                        Log.d("MethodChannel ", "Called");
                        prepareMada(totalAmount, SessionID, orderID, serviceType, orderType, custid, qty);
                    } else {
                        result.notImplemented();
                    }
                });
    }

    private void getAndroidId(MethodChannel.Result result) {
        try {
            String androidId = Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID);
            result.success(androidId);
        } catch (Exception e) {
            result.error("ERROR", "Failed to get Android ID: " + e.getMessage(), null);
        }
    }

    private void prepareMada(String totalAmount, String SessionID, String orderID, String serviceType, String orderType,
            String custid, String qty) {
        JsonObject prepareJsonData;
        MadaApiService madaApiService = new MadaApiService();

        prepareJsonData = new JsonObject();
        prepareJsonData.addProperty("CUSTID", custid);
        prepareJsonData.addProperty("PAYREFNO", orderID);
        prepareJsonData.addProperty("PAYTYPE", "D");
        prepareJsonData.addProperty("JSONREQS", createPayJson(totalAmount, orderID));

        // prepareJsonData.addProperty("JSONREQS", createPayJson);
        prepareJsonData.addProperty("ORDERTYPE", orderType);
        prepareJsonData.addProperty("qty", qty);
        prepareJsonData.addProperty("TOPUPAMT", totalAmount);
        prepareJsonData.addProperty("SERVICETYPE", serviceType);
        Log.d("prepareMada ", "Called");
        try {

            String session_id = SessionID;
            String api_version = "57";
            Log.d("createSession ", "Success");
            String html = "<html>" +
                    "<head>" +
                    "<meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">" +
                    "<script type=\"application/javascript\"" +
                    "src=\"" + MADA_URL + "/checkout/version/57/checkout.js\" " +
                    "data-error=\"errorCallback\"" +
                    "data-cancel=\"cancelCallback\">" +
                    "</script>" +
                    "<script type=\"text/javascript\">" +
                    "function errorCallback(error) " +
                    "{console.log(JSON.stringify(error));} " +
                    "function cancelCallback() {   android.goBack(); }" +
                    "function completeCallback() " +
                    "{console.log('Payment Complete');}" +
                    "Checkout.configure({merchant: '" + MADA_MID + "', " +
                    "order: { description: 'Aldrees', }, " +
                    "session: { id: '" + SessionID + "'  }, " +
                    "interaction: {merchant: {name: 'Aldrees', email: '<EMAIL>', phone: '+966118352514', logo: 'https://waie.aldrees.com/Images/aldreeslogonew.png', url: 'https://waie.aldrees.com', address: { line1: 'Riyadh', line2: 'KSA' } },"
                    +
                    "displayControl: {billingAddress: 'HIDE', customerEmail: 'HIDE', orderSummary: 'SHOW', paymentConfirmation: 'HIDE', paymentTerms: 'HIDE', }, }, });Checkout.showLightbox();</script> "
                    +
                    "</head>" +
                    "<body>" +
                    "<form >" +
                    "<input type=\"hidden\" run-in=\"server\" value=\"Pay with Lightbox \"onclick=\"Checkout.showLightbox();\" />"
                    +
                    "<input type=\"hidden\" value=\"Pay with Payment Page\" onclick=\"Checkout.showPaymentPage();\" />"
                    +
                    "</form>" +
                    "</body>" +
                    "</html>";
            Gateway.startHostedCheckoutActivity(this, html, orderID, prepareJsonData);

        } catch (Exception e) {
            Log.d("createSession ", "Fail 2 " + e.getMessage());
        }
    }

    public static void flutterReturn() {
        Log.d("flutterReturn ", "called");
        channel.invokeMethod("methodNameItz", null, new MethodChannel.Result() {
            @Override
            public void success(Object o) {
                // android.util.Log.d("Results success", o.toString());
                Log.d("flutterReturn ", "success");
            }

            @Override
            public void error(String s, String s1, Object o) {
                // android.util.Log.d("Results error", o.toString());
                Log.d("flutterReturn ", "error");
            }

            @Override
            public void notImplemented() {
                // android.util.Log.d("Results notImplemented", "");
                Log.d("flutterReturn ", "notImplemented");
            }
        });
    }

    private String createPayJson(String totalAmount, String orderID) {
        GatewayMap request = new GatewayMap()
                .set("apiOperation", "CREATE_CHECKOUT_SESSION")
                .set("interaction.operation", "PURCHASE")
                .set("interaction.returnUrl", "android://result")
                .set("order.amount", totalAmount)
                .set("order.currency", "SAR")
                .set("order.id", orderID)
                .set("order.reference", orderID)
                .set("transaction.reference", orderID);

        Gson gson = new GsonBuilder().create();
        return gson.toJson(request);
    }

}