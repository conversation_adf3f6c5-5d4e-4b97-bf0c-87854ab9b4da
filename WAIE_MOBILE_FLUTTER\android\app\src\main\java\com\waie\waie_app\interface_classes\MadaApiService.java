package com.waie.aldrees.mobile.interface_classes;

import android.os.Handler;
import android.os.Message;
import android.util.Base64;
import android.util.Pair;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import com.mastercard.gateway.android.sdk.Gateway;
import com.mastercard.gateway.android.sdk.GatewayCallback;
import com.mastercard.gateway.android.sdk.GatewayMap;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.SocketTimeoutException;
import java.net.URL;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;

import static android.text.TextUtils.isEmpty;

import io.flutter.Log;

public class MadaApiService implements IMadaApiService {
    public static String MADA_URL = "https://alahligatway.gateway.mastercard.com";
    public static String MADA_MID = "601095607";// ""TEST601095607";
    public static String MADA_USERNAME = "AldreesLive";
    public static String MADA_API_KEY = "1da1a9dc4c6c3768de45730e4492526c";// ""0107873ee1e9b4377b279dedc2d205ad";
    public static String MADA_VERSION = "57";
    public static String MADA_END_POINT = "https://endpoint.aldrees.com";
    private final Gateway gateway;
    private final String password;
    private String requestText, responseText;

    static final Gson GSON = new GsonBuilder().create();

    /*
     * @Inject
     * public MadaApiService() {
     * password = BuildConfig.MADA_KEY;
     * gateway = new Gateway();
     * gateway.setRegion(Gateway.Region.MTF);//TEST
     * gateway.setMerchantId(BuildConfig.MADA_MERCHANT);
     * }
     */

    public MadaApiService() {
        password = "";// MADA_API_KEY;
        gateway = new Gateway();
        gateway.setRegion(Gateway.Region.MTF);// TEST
        gateway.setMerchantId(MADA_MID);
    }

    /**
     * MAstercard servisi için url bilgisini oluşturur
     *
     * @return url
     */
    private String getApiUrl() {
        String prefix = "test-";
        switch (gateway.getRegion().name()) {
            case "ASIA_PACIFIC":
                prefix = "ap-";
                break;
            case "EUROPE":
                prefix = "eu-";
                break;
            case "NORTH_AMERICA":
                prefix = "na-";
                break;
            case "MTF":
                prefix = "test-";
                break;
        }
        /*
         * return "https://" + prefix +
         * "gateway.mastercard.com/api/rest/version/52/merchant/" +
         * gateway.getMerchantId();
         */
        return MADA_URL + "/api/rest/version/" + MADA_VERSION + "/merchant/" + gateway.getMerchantId();
    }

    /**
     * Bu metot mastercard servisinde oturum açar ve session bilgislerini döndürür.
     *
     * @param callback
     */
    @Override
    public void createSession(String amount, String orderId, CreateSessionCallback callback) {
        Log.d("createSession ", "Called");
        final Handler handler = new Handler(message -> {
            if (callback != null) {
                if (message.obj instanceof Throwable) {
                    callback.onError((Throwable) message.obj);
                } else {
                    Pair<String, String> pair = (Pair<String, String>) message.obj;
                    callback.onSuccess(pair.first, pair.second);
                }
            }
            return true;
        });

        new Thread(() -> {
            Message m = handler.obtainMessage();
            try {
                Log.d("MadaApiService ", "Called");
                m.obj = MadaApiService.this.executeCreateSession(amount, orderId);
            } catch (Exception e) {
                m.obj = e;
            }
            handler.sendMessage(m);
        }).start();
    }

    /**
     * Bu metot sessionid bilgisine göre mastercard servisindeki kart bilgilerini
     * günceller.
     *
     * @param sessionId       oturum id bilgisi
     * @param apiVersion      api versiyonu
     * @param request         güncellenen data bilgileri
     * @param gatewayCallback geri dönüş değerini almak için interface
     */
    @Override
    public void updateSession(String sessionId, String apiVersion, GatewayMap request,
            GatewayCallback gatewayCallback) {
        gateway.setRegion(Gateway.Region.ASIA_PACIFIC); // sonradan eklendi
        gateway.updateSession(sessionId, apiVersion, request, gatewayCallback);
    }

    @Override
    public String checkTransaction(String orderId) {
        String jsonResponse = null;
        JsonObject jsonobject = null;
        try {
            jsonResponse = doJsonRequest2(new URL(getApiUrl() + "/order/" + orderId), "", "GET",
                    "merchant." + gateway.getMerchantId(), password, HttpsURLConnection.HTTP_CREATED);
        } catch (MalformedURLException e) {
            e.printStackTrace();
        }

        /*
         * JsonObject jsonObject = new
         * JsonParser().parse(jsonResponse).getAsJsonObject();
         * JsonArray jsonArray = jsonObject.get("transaction").getAsJsonArray();
         * 
         * 
         * for (int i = 0; i < jsonArray.size(); i++) {
         * jsonobject = jsonArray.get(0).getAsJsonObject();
         * }
         * JsonObject response = jsonobject.getAsJsonObject("response");
         * String gatewayCode = response.get("gatewayCode").getAsString();
         * return gatewayCode;
         */
        return jsonResponse;
    }

    /**
     * Bu metot mastercard servisinde ödeme işlemi yaparak oturumunu tamamlar.
     *
     * @param jsonText      ödeme json verisi
     * @param orderId       sipariş id
     * @param transactionId işlem id
     * @param callback      dönüş değeri için interface
     */
    @Override
    public void completeSession(final String jsonText, final String orderId, final String transactionId,
            final String threeDSecure, final CompleteSessionCallback callback) {
        final Handler handler = new Handler(message -> {
            if (callback != null) {
                if (message.obj instanceof Throwable) {
                    callback.onError((Throwable) message.obj);
                } else {
                    callback.onSuccess((String) message.obj);
                }
            }
            return true;
        });

        new Thread(() -> {
            Message m = handler.obtainMessage();
            try {
                m.obj = executeCompleteSession(jsonText, orderId, transactionId);
            } catch (Exception e) {
                m.obj = e;
            }
            handler.sendMessage(m);
        }).start();
    }

    @Override
    public void check3dsEnrollment(String sessionId, String amount, String currency, String threeDSecureId,
            Check3DSecureEnrollmentCallback enrollmentCallback) {

    }

    GatewayMap executeCheck3DSEnrollment(String sessionId, String amount, String currency, String threeDSecureId)
            throws Exception {

        GatewayMap request = new GatewayMap()
                .set("apiOperation", "CHECK_3DS_ENROLLMENT")
                .set("session.id", sessionId)
                .set("order.amount", amount)
                .set("order.currency", currency)
                .set("3DSecure.authenticationRedirect.responseUrl",
                        "http://secure3d.aldrees.com/landing" + "/3DSecureResult.php?3DSecureId=" + threeDSecureId);

        String jsonRequest = GSON.toJson(request);

        String jsonResponse = doJsonRequest(new URL(getApiUrl() + "/3DSecureId/" + threeDSecureId), jsonRequest, "PUT",
                "merchant." + gateway.getMerchantId(), password, HttpsURLConnection.HTTP_OK);

        GatewayMap response = new GatewayMap(jsonResponse);

        if (!response.containsKey("response")) {
            throw new RuntimeException("Could not read gateway response");
        }

        // if there is an error result, throw it
        if (response.containsKey("response.result")
                && "ERROR".equalsIgnoreCase((String) response.get("response.result"))) {
            throw new RuntimeException("Check 3DS Enrollment Error: " + response.get("response.error.explanation"));
        }

        return response;
    }

    @Override
    public String getRequestText() {
        return requestText;
    }

    @Override
    public String getResponseText() {
        return responseText;
    }

    private Pair<String, String> executeCreateSession(String amount, String orderId) {
        GatewayMap request = new GatewayMap()
                .set("apiOperation", "CREATE_CHECKOUT_SESSION")
                .set("interaction.operation", "PURCHASE")
                .set("interaction.returnUrl", "android://result")
                .set("order.amount", amount)
                .set("order.currency", "SAR")
                .set("order.id", orderId)
                .set("order.reference", orderId)
                .set("transaction.reference", orderId);
        Log.d("Mada API ", "Called");

        String jsonRequest = GSON.toJson(request);

        String jsonResponse = null;
        try {
            jsonResponse = doJsonRequest(new URL(getApiUrl() + "/session"), jsonRequest, "POST",
                    "merchant." + gateway.getMerchantId(), password, HttpsURLConnection.HTTP_CREATED);
        } catch (MalformedURLException e) {
            e.printStackTrace();
        }

        GatewayMap response = new GatewayMap(jsonResponse);

        if (!response.containsKey("result") || !"SUCCESS".equalsIgnoreCase((String) response.get("result"))) {
            throw new RuntimeException("Create session result: " + response.get("result"));
        }

        String apiVersion = "57";
        String sessionId = (String) response.get("session.id");
        String successIndicator = (String) response.get("successIndicator");

        return new Pair<>(sessionId, apiVersion);
    }

    private String executeCompleteSession(String jsonText, String orderId, String transactionId) throws Exception {

        String jsonResponse = doJsonRequest(
                new URL(getApiUrl() + "/order/" + orderId + "/transaction/" + transactionId), jsonText, "PUT",
                "merchant." + gateway.getMerchantId(), password, HttpsURLConnection.HTTP_OK);
        GatewayMap response = new GatewayMap(jsonResponse);

        requestText = jsonText;
        responseText = jsonResponse;

        if (!response.containsKey("result") || !"SUCCESS".equalsIgnoreCase((String) response.get("result"))) {
            throw new RuntimeException("Error processing payment");
        }

        return (String) response.get("result");
    }

    /**
     * Initialise a new SSL context using the algorithm, key manager(s), trust
     * manager(s) and
     * source of randomness.
     *
     * @throws NoSuchAlgorithmException if the algorithm is not supported by the
     *                                  android platform
     * @throws KeyManagementException   if initialization of the context fails
     */
    private void initialiseSslContext() throws NoSuchAlgorithmException, KeyManagementException {
        SSLContext context = SSLContext.getInstance("TLS");
        context.init(null, null, null);
        HttpsURLConnection.setDefaultSSLSocketFactory(context.getSocketFactory());
    }

    /**
     * Open an HTTP or HTTPS connection to a particular URL
     *
     * @param address a valid HTTP[S] URL to connect to
     * @return an HTTP or HTTPS connection as appropriate
     * @throws KeyManagementException   if initialization of the SSL context fails
     * @throws NoSuchAlgorithmException if the SSL algorithm is not supported by the
     *                                  android platform
     * @throws MalformedURLException    if the address was not in the HTTP or HTTPS
     *                                  scheme
     * @throws IOException              if the connection could not be opened
     */
    private HttpURLConnection openConnection(URL address)
            throws KeyManagementException, NoSuchAlgorithmException, IOException {

        switch (address.getProtocol().toUpperCase()) {
            case "HTTPS":
                initialiseSslContext();
                break;
            case "HTTP":
                break;
            default:
                throw new MalformedURLException("Not an HTTP[S] address");
        }

        HttpURLConnection connection = (HttpURLConnection) address.openConnection();
        connection.setConnectTimeout(30000);
        connection.setReadTimeout(60000);
        return connection;
    }

    /**
     * Send a JSON object to an open HTTP[S] connection
     *
     * @param connection an open HTTP[S] connection, as returned by
     *                   {@link #openConnection(URL)}
     * @param method     an HTTP method, e.g. PUT, POST or GET
     * @param json       a valid JSON-formatted object
     * @param username   user name for basic authorization (can be null for no auth)
     * @param password   password for basic authorization (can be null for no auth)
     * @return an HTTP response code
     * @throws IOException if the connection could not be written to
     */
    private int makeJsonRequest(HttpURLConnection connection, String method, String json,
            String username, String password) throws IOException {

        connection.setDoOutput(true);
        connection.setRequestMethod(method);
        connection.setFixedLengthStreamingMode(json.getBytes().length);
        connection.setRequestProperty("Content-Type", "application/json");

        if (!isEmpty(username) && !isEmpty(password)) {
            String basicAuth = username + ':' + password;
            basicAuth = Base64.encodeToString(basicAuth.getBytes(), Base64.DEFAULT);
            connection.setRequestProperty("Authorization", "Basic " + basicAuth);
        }
        // Log.d("Connection",connection.toString());
        PrintWriter out = new PrintWriter(connection.getOutputStream());
        out.print(json);
        out.close();

        return connection.getResponseCode();
    }

    private int makeJsonRequest2(HttpURLConnection connection, String method, String json,
            String username, String password) throws IOException {

        connection.setRequestMethod(method);

        if (!isEmpty(username) && !isEmpty(password)) {
            String basicAuth = username + ':' + password;
            basicAuth = Base64.encodeToString(basicAuth.getBytes(), Base64.DEFAULT);
            connection.setRequestProperty("Authorization", "Basic " + basicAuth);
        }

        return connection.getResponseCode();
    }

    /**
     * Retrieve a JSON response from an open HTTP[S] connection. This would
     * typically be called
     * after
     *
     * @param connection an open HTTP[S] connection
     * @return a json object in string form
     * @throws IOException if the connection could not be read from
     */
    private String getJsonResponse(HttpURLConnection connection) throws IOException {
        StringBuilder responseOutput = new StringBuilder();
        String line;
        BufferedReader br = null;

        try {
            // If the HTTP response code is 4xx or 5xx, we need error rather than input
            // stream
            InputStream stream = (connection.getResponseCode() < 400)
                    ? connection.getInputStream()
                    : connection.getErrorStream();

            br = new BufferedReader(new InputStreamReader(stream));

            while ((line = br.readLine()) != null) {
                responseOutput.append(line);
            }
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (Exception e) {
                    /* Ignore close exception */
                }
            }
        }

        return responseOutput.toString();
    }

    /**
     * End-to-end method to send some json to an url and retrieve a response
     *
     * @param address             url to send the request to
     * @param jsonRequest         a valid JSON-formatted object
     * @param httpMethod          an HTTP method, e.g. PUT, POST or GET
     * @param username            user name for basic authorization (can be null for
     *                            no auth)
     * @param password            password for basic authorization (can be null for
     *                            no auth)
     * @param expectResponseCodes permitted HTTP response codes, e.g. HTTP_OK (200)
     * @return a json response object in string form
     */
    private String doJsonRequest(URL address, String jsonRequest, String httpMethod, String username, String password,
            int... expectResponseCodes) {
        Log.d("doJsonRequest ", "Called");
        HttpURLConnection connection;
        int responseCode;
        try {
            connection = openConnection(address);
        } catch (NoSuchAlgorithmException | KeyManagementException e) {
            e.printStackTrace();
            Log.d("doJsonRequest Error ", "Couldn't initialise SSL context");
            throw new RuntimeException("Couldn't initialise SSL context", e);
        } catch (IOException e) {
            e.printStackTrace();
            Log.d("doJsonRequest Error ", "Couldn't open an HTTP[S] connection");
            throw new RuntimeException("Couldn't open an HTTP[S] connection", e);
        }
        try {

            responseCode = makeJsonRequest(connection, httpMethod, jsonRequest, username, password);

            /*
             * if (!contains(expectResponseCodes, responseCode)) {
             * throw new RuntimeException("Unexpected response code " + responseCode);
             * }
             */
        } catch (SocketTimeoutException e) {
            Log.d("doJsonRequest Error ", "Timeout whilst sending JSON data");
            throw new RuntimeException("Timeout whilst sending JSON data");

        } catch (IOException e) {
            Log.d("doJsonRequest Error ", "Error sending JSON data");
            throw new RuntimeException("Error sending JSON data", e);
        }

        try {
            String responseBody = getJsonResponse(connection);

            if (responseBody == null) {
                throw new RuntimeException("No data in response");
            }

            return responseBody;
        } catch (SocketTimeoutException e) {
            throw new RuntimeException("Timeout whilst retrieving JSON response");
        } catch (IOException e) {
            throw new RuntimeException("Error retrieving JSON response", e);
        }
    }

    private String doJsonRequest2(URL address, String jsonRequest, String httpMethod, String username, String password,
            int... expectResponseCodes) {
        HttpURLConnection connection;
        int responseCode;
        try {
            connection = openConnection(address);
        } catch (NoSuchAlgorithmException | KeyManagementException e) {
            e.printStackTrace();
            throw new RuntimeException("Couldn't initialise SSL context", e);
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException("Couldn't open an HTTP[S] connection", e);
        }
        try {

            responseCode = makeJsonRequest2(connection, httpMethod, jsonRequest, username, password);

            /*
             * if (!contains(expectResponseCodes, responseCode)) {
             * throw new RuntimeException("Unexpected response code " + responseCode);
             * }
             */
        } catch (SocketTimeoutException e) {
            throw new RuntimeException("Timeout whilst sending JSON data");
        } catch (IOException e) {
            throw new RuntimeException("Error sending JSON data", e);
        }

        try {
            String responseBody = getJsonResponse(connection);

            if (responseBody == null) {
                throw new RuntimeException("No data in response");
            }

            return responseBody;
        } catch (SocketTimeoutException e) {
            throw new RuntimeException("Timeout whilst retrieving JSON response");
        } catch (IOException e) {
            throw new RuntimeException("Error retrieving JSON response", e);
        }
    }

    static boolean contains(int[] haystack, int needle) {
        for (int candidate : haystack) {
            if (candidate == needle) {
                return true;
            }
        }

        return false;
    }
}
